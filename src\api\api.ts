import { _axios,_axiosUpload }  from "./_axios";

export const uploadApi = (data:object)=>{
  return _axiosUpload("api/file/upload",data,"post")
}

export const loginApi = (data:object)=>{
  return _axios("api/admin/login",data,"post")
}
export const logoutApi = (data:object)=>{
  return _axios("api/admin/logout",data,"post")
}

export const adminMenusApi = (data:object)=>{
  return _axios("api/admin/menu",data,"get")
}

// 角色
export const roleApi = (data:object)=>{
  return _axios("api/role",data,"get")
}
export const roleAddApi = (data:object)=>{
  return _axios("api/role",data,"post")
}
export const roleUpdateApi = (id:any,data:object)=>{
  return _axios("api/role/"+id,data,"put")
}
export const roleRemoveApi = (id:number,data:object)=>{
  return _axios("api/role/"+id,data,"delete")
}
export const roleInfoApi = (id:number,data:object)=>{
  return _axios("api/role/"+id,data,"get")
}
export const roleRulesApi = (data:object)=>{
  return _axios("api/role/rules",data,"get")
}

// 管理员
export const adminApi = (data:object)=>{
  return _axios("api/admin",data,"get")
}
export const adminInfoApi = (id:number)=>{
  return _axios("api/admin/"+id,{},"get")
}
export const adminAddApi = (data:object)=>{
  return _axios("api/admin",data,"post")
}
export const adminUpdateApi = (data:any)=>{
  return _axios("api/admin/"+data.id,data,"put")
}
export const adminRemoveApi = (id:number,data:object)=>{
  return _axios("api/admin/"+id,data,"delete")
}
export const adminResetApi = (id:number,data:object)=>{
  return _axios("api/admin/"+id+"/reset",data,"post")
}
export const adminActiveApi = (id:number,status:string,data:object)=>{
  return _axios("api/admin/"+id+"/"+status,data,"post")
}
export const adminChangeSelfApi = (data:object)=>{
  return _axios("api/admin/selfs",data,"post")
}
export const adminChangePwdApi = (data:object)=>{
  return _axios("api/admin/password",data,"post")
}

// 部门
export const departmentApi = (data:object)=>{
  return _axios("api/department",data,"get")
}
export const departmentAddApi = (data:object)=>{
  return _axios("api/department",data,"post")
}
export const departmentUpdateApi = (data:any)=>{
  return _axios("api/department/"+data.id,data,"put")
}
export const departmentRemoveApi = (id:number,data:object)=>{
  return _axios("api/department/"+id,data,"delete")
}

// 日志管理
export const sysListApi = (data:object)=>{
  return _axios("api/syslog/list",data,"get")
}
export const sysInfoApi = (data:object)=>{
  return _axios("api/syslog/info/",data,"get")
}
export const sysRemoveApi = (data:object)=>{
  return _axios("api/syslog/remove",data,"post")
}

// 文章分类
export const articleCatListApi = (data:object)=>{
  return _axios("api/articlecat",data,"get")
}
export const articleCatAddApi = (data:object)=>{
  return _axios("api/articlecat",data,"post")
}
export const articleCatUpdateApi = (data:any)=>{
  return _axios("api/articlecat/"+data.id,data,"put")
}
export const articleCatRemoveApi = (id:number,data:object)=>{
  return _axios("api/articlecat/"+id,data,"delete")
}
export const articleCatInfoApi = (id:number,data:object)=>{
  return _axios("api/articlecat/"+id,data,"get")
}

// 文章模型
export const artModelListApi = (data:object)=>{
  return _axios("api/articlemodel",data,"get")
}
export const artModelAddApi = (data:object)=>{
  return _axios("api/articlemodel",data,"post")
}
export const artModelUpdateApi = (data:any)=>{
  return _axios("api/articlemodel/"+data.id,data,"put")
}
export const artModelRemoveApi = (id:number,data:object)=>{
  return _axios("api/articlemodel/"+id,data,"delete")
}
export const artModelInfoApi = (id:number,data:object)=>{
  return _axios("api/articlemodel/"+id,data,"get")
}

// 模型字段
export const artModelItemListApi = (modelId:any,data:object)=>{
  return _axios("api/articlemodelfield/"+modelId,data,"get")
}
export const artModelItemAddApi = (modelId:any,data:object)=>{
  return _axios("api/articlemodelfield/"+modelId,data,"post")
}
export const artModelItemUpdateApi = (modelId:any,data:any)=>{
  return _axios("api/articlemodelfield/"+modelId+'/'+data.id,data,"put")
}
export const artModelItemRemoveApi = (modelId:any,id:any,data:object)=>{
  return _axios("api/articlemodelfield/"+modelId+'/'+id,data,"delete")
}

// 内容管理
export const artArticleListApi = (data:object)=>{
  return _axios("api/article",data,"get")
}
export const artArticleAddApi = (data:object)=>{
  return _axios("api/article",data,"post")
}
export const artArticleUpdateApi = (data:any)=>{
  return _axios("api/article/"+data.id,data,"put")
}
export const artArticleRemoveApi = (id:number)=>{
  return _axios("api/article/"+id,{},"delete")
}
export const artArticleInfoApi = (id:number)=>{
  return _axios("api/article/"+id,{},"get")
}
export const artArticleTopApi = (id:number)=>{
  return _axios("api/article/"+id+'/top',{},"post")
}
export const artArticleDelTopApi = (id:number)=>{
  return _axios("api/article/"+id+'/top',{},"delete")
}
export const artArticleDisplayApi = (id:number)=>{
  return _axios("api/article/"+id+'/display',{},"post")
}
export const artArticleDelDisplayApi = (id:number)=>{
  return _axios("api/article/"+id+'/display',{},"delete")
}
export const artArticleAuditApi = (id:any)=>{
  return _axios("api/article/"+id+"/audit",{},"post")
}
export const artArticleDelAuditApi = (id:any)=>{
  return _axios("api/article/"+id+"/audit",{},"delete")
}
export const artArticlePutAuditApi = (id:any,data:object)=>{
  return _axios("api/article/"+id+"/audit",data,"put")
}

// 友链管理
export const linkApi = (data:object)=>{
  return _axios("api/friend",data,"get")
}
export const linkAddApi = (data:object)=>{
  return _axios("api/friend",data,"post")
}
export const linkUpdateApi = (data:any)=>{
  return _axios("api/friend/"+data.id,data,"put")
}
export const linkRemoveApi = (id:number,data:object)=>{
  return _axios("api/friend/"+id,data,"delete")
}

// 广告管理
export const entrancePosApi = (data:object)=>{
  return _axios("api/entrance/pos",data,"get")
}
export const bannersApi = (data:object)=>{
  return _axios("api/entrance",data,"get")
}
export const bannersUpdateApi = (data:any)=>{
  return _axios("api/entrance/"+data.id,data,"put")
}
export const bannersAddApi = (data:object)=>{
  return _axios("api/entrance",data,"post")
}
export const bannersRemoveApi = (id:number)=>{
  return _axios("api/entrance/"+id,{},"delete")
}

// 公共配置
export const comConfigFindApi = (data:object)=>{
  return _axios("api/aconfig/find",data,"get")
}
export const comConfindSaveApi = (data:object)=>{
  return _axios("api/aconfig/save",data,"post")
}

interface obj{
  code:string,
  type:string,
  data:string,
}
export const comConfigSaveApi = (data:Array<obj>)=>{
  return _axios("api/com-config/save",data,"post")
}