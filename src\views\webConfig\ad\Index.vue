<template>
  <div>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.name" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item prop="pos">
          <el-select v-model="form.pos" placeholder="请选择位置" clearable >
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option v-for="(item,index) in posList" :key="index" :label="item.tipTitle" :value="item.posKey" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addAd">添加广告</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序" width="60" />
        <el-table-column prop="name" label="名称" show-overflow-tooltip />
        <el-table-column prop="link" label="链接" show-overflow-tooltip />
        <el-table-column prop="pos" label="位置" />
        <el-table-column  label="图片" >
          <template #default="scope">
            <el-image v-if='scope.row.path' style="width: 100px;" :src="store.hw+scope.row.path" />
          </template>
        </el-table-column>
        <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.status?'启用':'禁用'}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope" >
            <el-button  type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button  type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getList' />
    </div>
    <AdPop ref="adPop" @changeData="getList" />
  </div>
</template>
<script lang="ts" setup>
import PageCom from '@/components/PageCom.vue'
import AdPop from './AdPop.vue'
import { ref,reactive,onMounted } from 'vue'
import { bannersApi,bannersRemoveApi,entrancePosApi } from '@/api/api'
import type { FormInstance } from 'element-plus'
import { useStore } from '@/store/Index'

const store = useStore()
let total = ref(0)
interface RuleForm {
  name: string
  status: string
  pos: string

}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  name:'',
  status:'',
  pos:'',
})

onMounted(()=>{
  getList()
  getPos()
})

const pageCom = ref()
let tableData = ref([])
const getList = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form
  }
  bannersApi(data).then((res:any) =>{
    console.log('广告管理',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}


interface  posType {
  tipTitle:string
  posKey:string
}

let posList:Ref<posType[]> = ref([])
const getPos = ()=>{
  entrancePosApi({}).then((res:any) =>{
    console.log('位置',res)
    if(res.code == 0){
      let arr:Array<posType> = []
      for (const key in res.data) {
        console.log(key)
        arr.push(res.data[key])
      }
      posList.value = arr
    }
  })
}

interface msg{
  id:number,
  status:boolean
}

const adPop = ref()
const addAd = ()=>{
  adPop.value.isEdit = false 
  adPop.value.posList = posList.value 
  adPop.value.open() 
}
const handleEdit = (row:msg)=>{
  adPop.value.isEdit = true 
  adPop.value.open() 
  adPop.value.form = JSON.parse(JSON.stringify(row)) 
  adPop.value.posList = posList.value 
}

const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    bannersRemoveApi(id).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

</script>